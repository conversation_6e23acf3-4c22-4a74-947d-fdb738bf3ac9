# SiniMind前端项目

改前端工程分为两个独立的项目： vue3 + react


## 目录结构

```
.
├── scripts                   // 开发环境启动脚本
├── src                       // vue的源代码目录
├── .env.development          // vue的环境变量配置(连测试环境)
├── .env.local                // vue的环境变量配置(连本地)
├── Dockerfile                // docker镜像构建配置
├── package.json              // vue的依赖配置
└── vite.config.ts            // vue的Vite配置文件
├── fastgpt-react             // fastgpt的代码目录
│   ├── src                   // fastgpt的源代码目录
│   ├── .env.development      // fastgpt的环境变量配置(连测试环境)
│   ├── .env.local            // fastgpt的环境变量配置(连本地)
│   ├── package.json          // fastgpt的依赖配置
│   ├── vite.config.ts        // fastgpt的Vite配置文件

```

## 启动说明

**必要条件：** Node.js 需要为 v20 版本

**注意：** 两个前端的配置是独立的，按需修改

1. 进入根目录，使用 yarn 安装依赖：

```bash
yarn install --registry=http://192.168.1.127:8088/repository/sinitek-npm
```

2. 配置开发环境变量，在  文件并配置以下变量：

`.env.development`、`fastgpt-react/.env.development` 默认配置的是测试环境的后端接口地址

`.env.local`、`fastgpt-react/.env.local` 配置的是本地的后端接口地址

```properties
# 开发环境标识
NODE_ENV=development

# 后端服务地址
VITE_BACKEND_URL=http://**************:8096
```

**注意：** Vite 环境变量必须以 `VITE_` 开头才能在客户端代码中访问。

3. 如果需要调整代理配置，请编辑 `vite.config.ts` 文件中的 server.proxy 配置：

```typescript
server: {
  proxy: {
    '/frontend': {
      target: env.VITE_BACKEND_URL,
      changeOrigin: true
    },
    '/mind': {
      target: env.VITE_BACKEND_URL,
      changeOrigin: true
    }
  }
}
```

4. 启动前端应用：

```bash
# 连接本地后端启动
yarn run dev

# 连接测试环境后端启动
yarn run dev:remote
```

访问地址：http://localhost:5173




## 代理工作原理

测试环境通过打包到同一个部署目录下实现同域模式的方式，共享localstorage，实现会话一致。

开发环境会启动两个前端，端口分别为5173、5000，通过自定义兼容菜单的url来传递token。






