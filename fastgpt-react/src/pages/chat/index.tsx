import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import NextHead from '@/components/common/NextHead';
import {useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { getInitChatInfo } from '@/web/core/chat/api';
import { Box, Flex, Drawer, DrawerOverlay, DrawerContent, useTheme, HStack } from '@chakra-ui/react';
import Avatar from '@/packages/components/common/Avatar';
import { streamFetch } from '@/web/common/api/fetch';
import { useChatStore } from '@/web/core/chat/context/useChatStore';
import { useToast } from '@/packages/hooks/useToast';
import { useTranslation } from 'react-i18next';

import type { StartChatFnProps } from '@/components/core/chat/ChatContainer/type';
import PageContainer from '@/components/PageContainer';
import SideBar from '@/components/SideBar';
import ChatHistorySlider from '@/pageComponents/chat/ChatHistorySlider';
import SliderApps from '@/pageComponents/chat/SliderApps';
import ChatHeader from '@/pageComponents/chat/ChatHeader';
import { useUserStore } from '@/web/support/user/useUserStore';

import { getChatTitleFromChatMessage } from '@/packages/global/core/chat/utils';
import { GPTMessages2Chats } from '@/packages/global/core/chat/adapt';
import { getMyApps } from '@/web/core/app/api';
import { useRequest2 } from '@/packages/hooks/useRequest';

import { useMount } from 'ahooks';
import { getNanoid } from '@/packages/global/common/string/tools';

import { GetChatTypeEnum } from '@/global/core/chat/constants';
import ChatContextProvider, { ChatContext } from '@/web/core/chat/context/chatContext';
import { type AppListItemType } from '@/packages/global/core/app/type';
import { useContextSelector } from 'use-context-selector';
import { lazy } from 'react';
import ChatBox from '@/components/core/chat/ChatContainer/ChatBox';
import { useSystem } from '@/packages/hooks/useSystem';
import { ChatSourceEnum } from '@/packages/global/core/chat/constants';
import ChatItemContextProvider, { ChatItemContext } from '@/web/core/chat/context/chatItemContext';
import ChatRecordContextProvider, {
  ChatRecordContext
} from '@/web/core/chat/context/chatRecordContext';
import ChatQuoteList from '@/pageComponents/chat/ChatQuoteList';

const CustomPluginRunBox = lazy(() => import('@/pageComponents/chat/CustomPluginRunBox'));

const Chat = ({ myApps }: { myApps: AppListItemType[] }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const theme = useTheme();
  const { t } = useTranslation();
  const { isPc } = useSystem();

  const { userInfo } = useUserStore();
  const { setLastChatAppId, chatId, appId, outLinkAuthData } = useChatStore();

  const isOpenSlider = useContextSelector(ChatContext, (v) => v.isOpenSlider);
  const onCloseSlider = useContextSelector(ChatContext, (v) => v.onCloseSlider);
  const forbidLoadChat = useContextSelector(ChatContext, (v) => v.forbidLoadChat);
  const onChangeChatId = useContextSelector(ChatContext, (v) => v.onChangeChatId);
  const onUpdateHistoryTitle = useContextSelector(ChatContext, (v) => v.onUpdateHistoryTitle);

  const resetVariables = useContextSelector(ChatItemContext, (v) => v.resetVariables);
  const isPlugin = useContextSelector(ChatItemContext, (v) => v.isPlugin);
  const chatBoxData = useContextSelector(ChatItemContext, (v) => v.chatBoxData);
  const setChatBoxData = useContextSelector(ChatItemContext, (v) => v.setChatBoxData);
  const datasetCiteData = useContextSelector(ChatItemContext, (v) => v.datasetCiteData);
  const setCiteModalData = useContextSelector(ChatItemContext, (v) => v.setCiteModalData);

  const chatRecords = useContextSelector(ChatRecordContext, (v) => v.chatRecords);
  const totalRecordsCount = useContextSelector(ChatRecordContext, (v) => v.totalRecordsCount);

  // Load chat init data
  const { loading } = useRequest2(
    async () => {
      if (!appId || forbidLoadChat.current) return;

      const res = await getInitChatInfo({ appId, chatId });
      res.userAvatar = userInfo?.avatar;

      // Wait for state update to complete
      setChatBoxData(res);

      // reset chat variables
      resetVariables({
        variables: res.variables,
        variableList: res.app?.chatConfig?.variables
      });
    },
    {
      manual: false,
      refreshDeps: [appId, chatId],
      onError(e: any) {
        // reset all chat tore
        if (e?.code === 501) {
          setLastChatAppId('');
          navigate('/dashboard/apps');
        } else {
          // 保留现有查询参数，只更新appId
          // const newSearchParams = new URLSearchParams(searchParams);
          // if (myApps[0]?._id) {
          //   newSearchParams.set('appId', myApps[0]._id);
          // }
          navigate(`/chat?appId=${myApps[0]._id}`, { replace: true });
        }
      },
      onFinally() {
        forbidLoadChat.current = false;
      }
    }
  );

  const onStartChat = useCallback(
    async ({
      messages,
      responseChatItemId,
      controller,
      generatingMessage,
      variables
    }: StartChatFnProps) => {
      // Just send a user prompt
      const histories = messages.slice(-1);
      const { responseText } = await streamFetch({
        data: {
          messages: histories,
          variables,
          responseChatItemId,
          appId,
          chatId
        },
        onMessage: generatingMessage,
        abortCtrl: controller
      });

      const newTitle = getChatTitleFromChatMessage(GPTMessages2Chats(histories)[0]);

      // new chat
      onUpdateHistoryTitle({ chatId, newTitle });
      // update chat window
      setChatBoxData((state) => ({
        ...state,
        title: newTitle
      }));

      return { responseText, isNewChat: forbidLoadChat.current };
    },
    [appId, chatId, onUpdateHistoryTitle, setChatBoxData, forbidLoadChat]
  );

  const RenderHistorySlider = useMemo(() => {
    const Children = (
      <ChatHistorySlider confirmClearText={t('common:core.chat.Confirm to clear history')} />
    );

    return isPc || !appId ? (
      <SideBar externalTrigger={!!datasetCiteData}>{Children}</SideBar>
    ) : (
      <Drawer
        isOpen={isOpenSlider}
        placement="left"
        autoFocus={false}
        size={'xs'}
        onClose={onCloseSlider}
      >
        <DrawerOverlay backgroundColor={'rgba(255,255,255,0.5)'} />
        <DrawerContent maxWidth={'75vw'}>{Children}</DrawerContent>
      </Drawer>
    );
  }, [t, isPc, appId, isOpenSlider, onCloseSlider, datasetCiteData]);

  return (
    <Flex h={'100%'}>
      <NextHead title={chatBoxData.app.name} icon={chatBoxData.app.avatar}></NextHead>
      {/* pc show myself apps */}
      {isPc && (
        <Box borderRight={theme.borders.base} flex={'0 0 220px'}>
          <SliderApps apps={myApps} activeAppId={appId} />
        </Box>
      )}

      {(!datasetCiteData || isPc) && (
        <PageContainer flex={'1 0 0'} w={0} p={[0, '16px']} position={'relative'}>
          <Flex h={'100%'} flexDirection={['column', 'row']}>
            {/* pc always show history. */}
            {RenderHistorySlider}
            {/* chat container */}
            <Flex
              position={'relative'}
              h={[0, '100%']}
              w={['100%', 0]}
              flex={'1 0 0'}
              flexDirection={'column'}
            >
              {/* header */}
              <ChatHeader
                totalRecordsCount={totalRecordsCount}
                apps={myApps}
                history={chatRecords}
                showHistory
              />

              {/* chat box */}
              <Box flex={'1 0 0'} bg={'white'}>
                {isPlugin ? (
                  <CustomPluginRunBox
                    appId={appId}
                    chatId={chatId}
                    outLinkAuthData={outLinkAuthData}
                    onNewChat={() => onChangeChatId(getNanoid())}
                    onStartChat={onStartChat}
                  />
                ) : (
                  <ChatBox
                    appId={appId}
                    chatId={chatId}
                    outLinkAuthData={outLinkAuthData}
                    showEmptyIntro
                    feedbackType={'user'}
                    onStartChat={onStartChat}
                    chatType={'chat'}
                    isReady={!loading}
                  />
                )}
              </Box>
            </Flex>
          </Flex>
        </PageContainer>
      )}

      {datasetCiteData && (
        <PageContainer flex={'1 0 0'} w={0} maxW={'560px'}>
          <ChatQuoteList
            rawSearch={datasetCiteData.rawSearch}
            metadata={datasetCiteData.metadata}
            onClose={() => setCiteModalData(undefined)}
          />
        </PageContainer>
      )}
    </Flex>
  );
};

const Render = () => {
  const [searchParams] = useSearchParams();

  const { t } = useTranslation();
  const { toast } = useToast();
  const navigate = useNavigate();
  const theme = useTheme();
  const { isPc } = useSystem();
  const { source, chatId, lastChatAppId, setSource, setAppId, appId: storeAppId } = useChatStore();

  // 从 URL 参数中获取 appId 和 isStandalone
  const appId = searchParams.get('appId') || '';
  const isStandalone = searchParams.get('isStandalone') || '';

  // 1. 获取应用列表
  const { data: myApps = [], loading: appsLoading } = useRequest2(
    () => getMyApps({ getRecentlyChat: true }),
    {
      manual: false
    }
  );

  // 使用ref来跟踪处理状态，避免重复执行
  const initProcessed = useRef(false);
  const lastSetAppId = useRef('');

  // 2. 处理应用选择逻辑 - 只在必要时执行
  useEffect(() => {
    console.log('🔄 useEffect triggered:', { appsLoading, appId, storeAppId, myAppsLength: myApps.length });

    // 等待应用列表加载完成
    if (appsLoading) return;

    // 检查是否有应用
    if (myApps.length === 0) {
      toast({
        status: 'error',
        title: t('common:core.chat.You need to a chat app')
      });
      navigate('/dashboard/apps', { replace: true });
      return;
    }

    // 如果URL中没有appId，重定向到默认应用（只执行一次）
    if (!appId) {
      if (!initProcessed.current) {
        initProcessed.current = true;
        const targetAppId = lastChatAppId || myApps[0]._id || '';
        console.log('🚀 Redirecting to default app:', targetAppId);
        if (targetAppId) {
          navigate(`/chat?appId=${targetAppId}`, { replace: true });
        }
      }
      return;
    }

    // 设置source（只设置一次）
    if (source !== ChatSourceEnum.online) {
      console.log('🔧 Setting source to online');
      setSource('online');
    }

    // 只有当appId真正发生变化时才设置
    if (appId && appId !== storeAppId && appId !== lastSetAppId.current) {
      console.log('🎯 Setting appId:', { from: storeAppId, to: appId });
      lastSetAppId.current = appId;
      setAppId(appId);
    }
  }, [appsLoading, myApps.length, appId]); // 最小化依赖，避免循环

  const chatHistoryProviderParams = useMemo(
    () => ({ appId, source: ChatSourceEnum.online }),
    [appId]
  );
  const chatRecordProviderParams = useMemo(() => {
    return {
      appId,
      type: GetChatTypeEnum.normal,
      chatId: chatId
    };
  }, [appId, chatId]);

  // 创建一个自定义的应用切换处理函数
  const handleAppChange = useCallback((newAppId: string) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('appId', newAppId);
    navigate(`/chat?${newSearchParams.toString()}`);
  }, [navigate, searchParams]);

  // 3. 只有在应用列表加载完成且有有效appId时才渲染对话框
  if (appsLoading || !appId) {
    return (
      <Flex h={'100%'}>
          <Flex h={'100%'} align={'center'} justify={'center'}>
            <Box>正在初始化聊天...</Box>
          </Flex>
      </Flex>
    );
  }

  return source === ChatSourceEnum.online ? (
    <ChatContextProvider params={chatHistoryProviderParams}>
      <ChatItemContextProvider
        showRouteToAppDetail={isStandalone !== '1'}
        showRouteToDatasetDetail={isStandalone !== '1'}
        isShowReadRawSource={true}
        isResponseDetail={true}
        // isShowFullText={true}
        showNodeStatus
      >
        <ChatRecordContextProvider params={chatRecordProviderParams}>
          <Chat myApps={myApps} />
        </ChatRecordContextProvider>
      </ChatItemContextProvider>
    </ChatContextProvider>
  ) : null;
};



export default Render;
