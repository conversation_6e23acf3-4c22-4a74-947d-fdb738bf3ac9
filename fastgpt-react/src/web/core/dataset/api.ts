import { GET, POST, PUT, DELETE } from '@/web/common/api/request';
import type {
  GetPathProps,
  ParentTreePathItemType
} from '@/packages/global/common/parentFolder/type.d';
import type {
  DatasetItemType,
  DatasetListItemType,
  DatasetSimpleItemType,
  DatasetTagType,
  TagUsageType
} from '@/packages/global/core/dataset/type.d';
import type { GetDatasetCollectionsProps } from '@/global/core/api/datasetReq.d';
import type {
  AddTagsToCollectionsParams,
  ApiDatasetCreateDatasetCollectionParams,
  CreateDatasetCollectionParams,
  CreateDatasetCollectionTagParams,
  DatasetUpdateBody,
  ExternalFileCreateDatasetCollectionParams,
  FileIdCreateDatasetCollectionParams,
  reTrainingDatasetFileCollectionParams,
  LinkCreateDatasetCollectionParams,
  PostWebsiteSyncParams,
  TextCreateDatasetCollectionParams,
  UpdateDatasetCollectionTagParams
} from '@/packages/global/core/dataset/api.d';
import type { SearchTestProps, SearchTestResponse } from '@/global/core/dataset/api.d';
import type { CreateDatasetParams, InsertOneDatasetDataProps } from '@/global/core/dataset/api.d';
import type { DatasetCollectionItemType } from '@/packages/global/core/dataset/type';
import type { DatasetCollectionSyncResultEnum } from '@/packages/global/core/dataset/constants';
import type { DatasetDataItemType } from '@/packages/global/core/dataset/type';
import type { DatasetCollectionsListItemType } from '@/global/core/dataset/type.d';
// import type { getDatasetTrainingQueueResponse } from '@/pages/api/core/dataset/training/getDatasetTrainingQueue';
type getDatasetTrainingQueueResponse = {
  rebuildingCount: number;
  trainingCount: number;
};
// import type { rebuildEmbeddingBody } from '@/pages/api/core/dataset/training/rebuildEmbedding';
type rebuildEmbeddingBody = {
  datasetId: string;
  vectorModel: string;
};

import type { readCollectionSourceBody, readCollectionSourceResponse } from '@/components/core/dataset/read';
import { DatasetTypeEnum } from '@/packages/global/core/dataset/constants';
import { type ParentIdType } from '@/packages/global/common/parentFolder/type';
type GetDatasetListBody = {
  parentId: ParentIdType;
  type?: DatasetTypeEnum;
  searchKey?: string;
};
// import type { UpdateDatasetCollectionParams } from '@/pages/api/core/dataset/collection/update';
type UpdateDatasetCollectionParams = {
  id?: string;
  parentId?: string;
  name?: string;
  tags?: string[]; // Not tag id, is tag label
  forbid?: boolean;
  createTime?: Date;

  // External file id
  datasetId?: string;
  externalFileId?: string;
};

import type { DatasetDataListItemType } from '@/global/core/dataset/type';
type GetDatasetDataListProps = PaginationProps & {
  searchText?: string;
  collectionId: string;
};
type GetDatasetDataListRes = PaginationResponse<DatasetDataListItemType>;
import type { UpdateDatasetDataProps } from '@/packages/global/core/dataset/controller';
// import type { DatasetFolderCreateBody } from '@/pages/api/core/dataset/folder/create';
type DatasetFolderCreateBody = {
  parentId?: string;
  name: string;
  intro: string;
};
import type { PaginationProps, PaginationResponse } from '@/packages/common/fetch/type';
// import type { GetApiDatasetFileListProps } from '@/pages/api/core/dataset/apiDataset/list';
type GetApiDatasetFileListProps = {
  searchKey?: string;
  parentId?: ParentIdType;
  datasetId: string;
};
// import type {
//   listExistIdQuery,
//   listExistIdResponse
// } from '@/pages/api/core/dataset/apiDataset/listExistId';
type listExistIdQuery = {
  datasetId: string;
};
type listExistIdResponse = string[];
import { type DatasetCollectionSchemaType } from '@/packages/global/core/dataset/type';
type GetQuoteDataResponse = {
  collection: DatasetCollectionSchemaType;
  q: string;
  a?: string;
};
// import type { GetQuotePermissionResponse } from '@/pages/api/core/dataset/data/getPermission';
type GetQuotePermissionResponse =
    | {
  datasetName: string;
  permission: {
    hasWritePer: boolean;
    hasReadPer: boolean;
  };
}
    | undefined;
// import type { GetQueueLenResponse } from '@/pages/api/core/dataset/training/getQueueLen';
type GetQueueLenResponse = {
  vectorTrainingCount: number;
  qaTrainingCount: number;
  autoTrainingCount: number;
  imageTrainingCount: number;
};
// import type { updateTrainingDataBody } from '@/pages/api/core/dataset/training/updateTrainingData';
type updateTrainingDataBody = {
  datasetId: string;
  collectionId: string;
  dataId: string;
  q?: string;
  a?: string;
  chunkIndex?: number;
};
// import type {
//   getTrainingDataDetailBody,
//   getTrainingDataDetailResponse
// } from '@/pages/api/core/dataset/training/getTrainingDataDetail';
type getTrainingDataDetailBody = {
  datasetId: string;
  collectionId: string;
  dataId: string;
};
type getTrainingDataDetailResponse =
    | {
  _id: string;
  datasetId: string;
  mode: string;
  q?: string;
  a?: string;
  imagePreviewUrl?: string;
}
    | undefined;
// import type { deleteTrainingDataBody } from '@/pages/api/core/dataset/training/deleteTrainingData';
type deleteTrainingDataBody = {
  datasetId: string;
  collectionId: string;
  dataId: string;
};
import type {
  DatasetCollectionDataProcessModeEnum,
  TrainingModeEnum
} from '@/packages/global/core/dataset/constants';
type getTrainingDetailResponse = {
  trainingType: DatasetCollectionDataProcessModeEnum;
  advancedTraining: {
    customPdfParse: boolean;
    imageIndex: boolean;
    autoIndexes: boolean;
  };
  queuedCounts: Record<TrainingModeEnum, number>;
  trainingCounts: Record<TrainingModeEnum, number>;
  errorCounts: Record<TrainingModeEnum, number>;
  trainedCount: number;
};
import { type DatasetTrainingSchemaType } from '@/packages/global/core/dataset/type';
type getTrainingErrorBody = PaginationProps<{
  collectionId: string;
}>;
type getTrainingErrorResponse = PaginationResponse<DatasetTrainingSchemaType>;
import type { ApiDatasetServerType, APIFileItem } from '@/packages/global/core/dataset/apiDataset/type';
import { type OutLinkChatAuthProps } from '@/packages/global/support/permission/chat';
type GetQuoteDataProps =
    | {
  id: string;
}
    | ({
  id: string;
  appId: string;
  chatId: string;
  chatItemDataId: string;
} & OutLinkChatAuthProps);
// import type {
//   GetApiDatasetCataLogResponse,
//   GetApiDatasetCataLogProps
// } from '@/pages/api/core/dataset/apiDataset/getCatalog';
type GetApiDatasetCataLogResponse = APIFileItem[];
type GetApiDatasetCataLogProps = {
  parentId?: ParentIdType;
  apiDatasetServer?: ApiDatasetServerType;
};
// import type {
//   GetApiDatasetPathBody,
//   GetApiDatasetPathResponse
// } from '@/pages/api/core/dataset/apiDataset/getPathNames';
type GetApiDatasetPathBody = {
  datasetId?: string;
  parentId?: ParentIdType;
  apiDatasetServer?: ApiDatasetServerType;
};
type GetApiDatasetPathResponse = string;


import type { ChunkSettingsType } from '@/packages/global/core/dataset/type';
import { DatasetSourceReadTypeEnum } from '@/packages/global/core/dataset/constants';
type PostPreviewFilesChunksProps = ChunkSettingsType & {
  datasetId: string;
  type: DatasetSourceReadTypeEnum;
  sourceId: string;

  customPdfParse?: boolean;

  // Chunk settings
  overlapRatio: number;

  // Read params
  selector?: string;
  externalFileId?: string;
};
type PreviewChunksResponse = {
  chunks: {
    q: string;
    a: string;
  }[];
  total: number;
};

/* ======================== dataset ======================= */
export const getDatasets = (data: GetDatasetListBody) =>
  POST<DatasetListItemType[]>(`/api/core/dataset/list`, data);

export const getDatasetsByAppIdAndDatasetIds = (data: { appId: string; datasetIdList: string[] }) =>
  POST<DatasetSimpleItemType[]>(`/api/core/dataset/listByAppIdAndDatasetIds`, data);
/**
 * get type=dataset list
 */

export const getDatasetPaths = (data: GetPathProps) => {
  if (!data.sourceId) return Promise.resolve([]);
  return GET<ParentTreePathItemType[]>('/api/core/dataset/paths', data);
};

export const getDatasetById = (id: string) => GET<DatasetItemType>(`/api/core/dataset/detail?id=${id}`);

export const postCreateDataset = (data: CreateDatasetParams) =>
  POST<string>(`/api/core/dataset/create`, data);

export const putDatasetById = (data: DatasetUpdateBody) => PUT<void>(`/api/core/dataset/update`, data);

export const delDatasetById = (id: string) => POST(`/api/core/dataset/delete?id=${id}`);

export const postWebsiteSync = (data: PostWebsiteSyncParams) =>
  POST(`/api/core/dataset/websiteSync`, data, {
    timeout: 600000
  }).catch();

export const postCreateDatasetFolder = (data: DatasetFolderCreateBody) =>
  POST(`/api/core/dataset/folder/create`, data);

export const resumeInheritPer = (datasetId: string) =>
  GET(`/api/core/dataset/resumeInheritPermission`, { datasetId });

export const postChangeOwner = (data: { ownerId: string; datasetId: string }) =>
  POST(`/api/core/dataset/changeOwner`, data);

export const postBackupDatasetCollection = ({
  file,
  percentListen,
  datasetId
}: {
  file: File;
  percentListen: (percent: number) => void;
  datasetId: string;
}) => {
  const formData = new FormData();
  formData.append('file', file, encodeURIComponent(file.name));
  formData.append('data', JSON.stringify({ datasetId }));

  return POST(`/api/core/dataset/collection/create/backup`, formData, {
    timeout: 600000,
    onUploadProgress: (e) => {
      if (!e.total) return;

      const percent = Math.round((e.loaded / e.total) * 100);
      percentListen?.(percent);
    },
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });
};
export const postTemplateDatasetCollection = ({
  file,
  percentListen,
  datasetId
}: {
  file: File;
  percentListen: (percent: number) => void;
  datasetId: string;
}) => {
  const formData = new FormData();
  formData.append('file', file, encodeURIComponent(file.name));
  formData.append('data', JSON.stringify({ datasetId }));

  return POST(`/api/core/dataset/collection/create/template`, formData, {
    timeout: 600000,
    onUploadProgress: (e) => {
      if (!e.total) return;

      const percent = Math.round((e.loaded / e.total) * 100);
      percentListen?.(percent);
    },
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    }
  });
};

/* =========== search test ============ */
export const postSearchText = (data: SearchTestProps) =>
  POST<SearchTestResponse>(`/api/core/dataset/searchTest`, data);

/* ============================= collections ==================================== */
export const getDatasetCollections = (data: GetDatasetCollectionsProps) =>
  POST<PaginationResponse<DatasetCollectionsListItemType>>(`/api/core/dataset/collection/listV2`, data);
export const getDatasetCollectionPathById = (parentId: string) =>
  GET<ParentTreePathItemType[]>(`/api/core/dataset/collection/paths`, { parentId });
export const getDatasetCollectionById = (id: string) =>
  GET<DatasetCollectionItemType>(`/api/core/dataset/collection/detail`, { id });
export const getDatasetCollectionTrainingDetail = (collectionId: string) =>
  GET<getTrainingDetailResponse>(`/api/core/dataset/collection/trainingDetail`, {
    collectionId
  });
export const postDatasetCollection = (data: CreateDatasetCollectionParams) =>
  POST<string>(`/api/core/dataset/collection/create`, data);
export const postCreateDatasetFileCollection = (data: FileIdCreateDatasetCollectionParams) =>
  POST<{ collectionId: string }>(`/api/core/dataset/collection/create/fileId`, data, {
    timeout: 360000
  });
export const postReTrainingDatasetFileCollection = (data: reTrainingDatasetFileCollectionParams) =>
  POST<{ collectionId: string }>(`/api/core/dataset/collection/create/reTrainingCollection`, data, {
    timeout: 360000
  });
export const postCreateDatasetLinkCollection = (data: LinkCreateDatasetCollectionParams) =>
  POST<{ collectionId: string }>(`/api/core/dataset/collection/create/link`, data);
export const postCreateDatasetTextCollection = (data: TextCreateDatasetCollectionParams) =>
  POST<{ collectionId: string }>(`/api/core/dataset/collection/create/text`, data);

export const postCreateDatasetExternalFileCollection = (
  data: ExternalFileCreateDatasetCollectionParams
) =>
  POST<{ collectionId: string }>(`/api/core/dataset/collection/create/externalFileUrl`, data, {
    timeout: 360000
  });
export const postCreateDatasetApiDatasetCollection = (
  data: ApiDatasetCreateDatasetCollectionParams
) =>
  POST<{ collectionId: string }>(`/api/core/dataset/collection/create/apiCollection`, data, {
    timeout: 360000
  });

export const putDatasetCollectionById = (data: UpdateDatasetCollectionParams) =>
  POST(`/api/core/dataset/collection/update`, data);
export const moveDatasetCollectionById = (data: UpdateDatasetCollectionParams) =>
  POST(`/api/core/dataset/collection/move`, data);
export const delDatasetCollectionById = (params: { id: string }) =>
  DELETE(`/api/core/dataset/collection/delete`, params);
export const postLinkCollectionSync = (collectionId: string) =>
  POST<DatasetCollectionSyncResultEnum>(`/api/core/dataset/collection/sync`, {
    collectionId
  });

/* =============================== tag ==================================== */

export const postCreateDatasetCollectionTag = (data: CreateDatasetCollectionTagParams) =>
  POST(`/api/core/dataset/tag/create`, data);
export const postAddTagsToCollections = (data: AddTagsToCollectionsParams) =>
  POST(`/api/core/dataset/tag/addToCollections`, data);
export const delDatasetCollectionTag = (data: { id: string; datasetId: string }) =>
  DELETE(`/api/core/dataset/tag/delete`, data);
export const updateDatasetCollectionTag = (data: UpdateDatasetCollectionTagParams) =>
  POST(`/api/core/dataset/tag/update`, data);
export const getDatasetCollectionTags = (
  data: PaginationProps<{
    datasetId: string;
    searchText?: string;
  }>
) => POST<PaginationResponse<DatasetTagType>>(`/api/core/dataset/tag/list`, data);
export const getTagUsage = (datasetId: string) =>
  GET<TagUsageType[]>(`/api/core/dataset/tag/tagUsage?datasetId=${datasetId}`);
export const getAllTags = (datasetId: string) =>
  GET<{ list: DatasetTagType[] }>(`/api/core/dataset/tag/getAllTags?datasetId=${datasetId}`);

/* =============================== data ==================================== */
/* get dataset list */
export const getDatasetDataList = (data: GetDatasetDataListProps) =>
  POST<GetDatasetDataListRes>(`/api/core/dataset/data/v2/list`, data);

export const getDatasetDataPermission = (id?: string) =>
  GET<GetQuotePermissionResponse>(`/api/core/dataset/data/getPermission`, { id });

export const getDatasetDataItemById = (id: string) =>
  GET<DatasetDataItemType>(`/api/core/dataset/data/detail`, { id });

/**
 * insert one data to dataset (immediately insert)
 */
export const postInsertData2Dataset = (data: InsertOneDatasetDataProps) =>
  POST<string>(`/api/core/dataset/data/insertData`, data);

/**
 * update one datasetData by id
 */
export const putDatasetDataById = (data: UpdateDatasetDataProps) =>
  PUT('/api/core/dataset/data/update', data);
/**
 * 删除一条知识库数据
 */
export const delOneDatasetDataById = (id: string) =>
  DELETE<string>(`/api/core/dataset/data/delete`, { id });

// Get quote data
export const getQuoteData = (data: GetQuoteDataProps) =>
  POST<GetQuoteDataResponse>(`/api/core/dataset/data/getQuoteData`, data);

/* ================ training ==================== */
export const postRebuildEmbedding = (data: rebuildEmbeddingBody) =>
  POST(`/api/core/dataset/training/rebuildEmbedding`, data);

/* get length of system training queue */
export const getTrainingQueueLen = () =>
  GET<GetQueueLenResponse>(`/api/core/dataset/training/getQueueLen`);
export const getDatasetTrainingQueue = (datasetId: string) =>
  GET<getDatasetTrainingQueueResponse>(`/api/core/dataset/training/getDatasetTrainingQueue`, {
    datasetId
  });

export const getPreviewChunks = (data: PostPreviewFilesChunksProps) =>
  POST<PreviewChunksResponse>('/api/core/dataset/file/getPreviewChunks', data, {
    maxQuantity: 1,
    timeout: 600000
  });

export const deleteTrainingData = (data: deleteTrainingDataBody) =>
  POST(`/api/core/dataset/training/deleteTrainingData`, data);
export const updateTrainingData = (data: updateTrainingDataBody) =>
  PUT(`/api/core/dataset/training/updateTrainingData`, data);
export const getTrainingDataDetail = (data: getTrainingDataDetailBody) =>
  POST<getTrainingDataDetailResponse>(`/api/core/dataset/training/getTrainingDataDetail`, data);
export const getTrainingError = (data: getTrainingErrorBody) =>
  POST<getTrainingErrorResponse>(`/api/core/dataset/training/getTrainingError`, data);

/* ================== read source ======================== */
export const getCollectionSource = (data: readCollectionSourceBody) =>
  POST<readCollectionSourceResponse>('/api/core/dataset/collection/read', data);

/* ================== apiDataset ======================== */
export const getApiDatasetFileList = (data: GetApiDatasetFileListProps) =>
  POST<APIFileItem[]>('/api/core/dataset/apiDataset/list', data);
export const getApiDatasetFileListExistId = (data: listExistIdQuery) =>
  GET<listExistIdResponse>('/api/core/dataset/apiDataset/listExistId', data);

export const getApiDatasetCatalog = (data: GetApiDatasetCataLogProps) =>
  POST<GetApiDatasetCataLogResponse>('/api/core/dataset/apiDataset/getCatalog', data);

export const getApiDatasetPaths = (data: GetApiDatasetPathBody) =>
  POST<GetApiDatasetPathResponse>('/api/core/dataset/apiDataset/getPathNames', data);
