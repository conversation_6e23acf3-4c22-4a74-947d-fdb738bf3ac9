#!/usr/bin/env node

import { spawn } from 'child_process';
import net from 'net';
import fs from 'fs';
import path from 'path';

// 日志函数
function log(level, message, ...args) {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📝',
    success: '✅',
    error: '❌',
    warn: '⚠️',
    debug: '🔍'
  }[level] || '📝';

  console.log(`${prefix} [${timestamp}] ${message}`, ...args);
}

// 获取环境配置
function getEnvConfig() {
  const envFile = process.env.VITE_ENV_FILE || '.env.development';
  const envPath = path.resolve(process.cwd(), envFile);

  if (!fs.existsSync(envPath)) {
    log('error', `环境文件不存在: ${envFile}`);
    process.exit(1);
  }

  log('info', `使用环境配置文件: ${envFile}`);
  return envFile;
}

// 检查端口是否被占用
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(port, () => {
      server.once('close', () => resolve(true));
      server.close();
    });
    server.on('error', () => resolve(false));
  });
}

// 检查必要文件是否存在
function checkRequiredFiles() {
  const requiredFiles = [
    'package.json',
    'vite.config.ts',
    'fastgpt-react/package.json',
    'fastgpt-react/vite.config.ts'
  ];

  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      log('error', `必需文件不存在: ${file}`);
      return false;
    }
  }

  log('success', '必需文件检查通过');
  return true;
}

// 检查依赖是否安装
function checkDependencies() {
  const nodeModulesExists = fs.existsSync('node_modules');
  const reactNodeModulesExists = fs.existsSync('fastgpt-react/node_modules');

  if (!nodeModulesExists) {
    log('error', '根目录 node_modules 不存在，请运行 npm install');
    return false;
  }

  if (!reactNodeModulesExists) {
    log('error', 'React应用 node_modules 不存在，请运行 npm install');
    return false;
  }

  log('success', '依赖检查通过');
  return true;
}

// 启动进程
function startProcess(command, args, cwd, name, env = {}) {
  log('info', `启动 ${name}...`);

  // 合并环境变量
  const processEnv = {
    ...process.env,
    ...env
  };

  const childProcess = spawn(command, args, {
    cwd,
    stdio: 'inherit',
    shell: true,
    env: processEnv
  });

  childProcess.on('error', (err) => {
    log('error', `${name} 启动失败:`, err.message);
  });

  childProcess.on('exit', (code, signal) => {
    if (code !== 0 && code !== null) {
      log('error', `${name} 异常退出，代码: ${code}`);
    } else if (signal) {
      log('warn', `${name} 被信号终止: ${signal}`);
    }
  });

  return childProcess;
}

// 健康检查
async function healthCheck() {
  log('debug', '开始健康检查...');

  // 检查Vue3服务器
  try {
    const response = await fetch('http://localhost:5173');
    if (response.ok) {
      log('success', 'Vue3服务器健康检查通过');
    }
  } catch (err) {
    log('warn', 'Vue3服务器健康检查失败，可能还在启动中');
  }

  // 检查React服务器
  try {
    const response = await fetch('http://localhost:5100');
    if (response.ok) {
      log('success', 'React服务器健康检查通过');
    }
  } catch (err) {
    log('warn', 'React服务器健康检查失败，可能还在启动中');
  }
}

async function main() {
  log('info', '开始启动双框架开发环境...');

  // 获取环境配置
  const envFile = getEnvConfig();

  // 检查必要文件
  if (!checkRequiredFiles()) {
    process.exit(1);
  }

  // 检查依赖
  if (!checkDependencies()) {
    log('warn', '建议运行 npm install 安装依赖');
  }

  log('debug', '检查端口可用性...');

  // 检查Vue3端口 (5173)
  const vue3PortAvailable = await checkPort(5173);
  if (!vue3PortAvailable) {
    log('error', '端口 5173 已被占用，请关闭占用该端口的程序');
    log('info', '可以使用以下命令查找占用端口的进程:');
    log('info', '  Windows: netstat -ano | findstr :5173');
    log('info', '  macOS/Linux: lsof -ti:5173');
    process.exit(1);
  }

  // 检查React端口 (5100)
  const reactPortAvailable = await checkPort(5100);
  if (!reactPortAvailable) {
    log('error', '端口 5100 已被占用，请关闭占用该端口的程序');
    log('info', '可以使用以下命令查找占用端口的进程:');
    log('info', '  Windows: netstat -ano | findstr :5100');
    log('info', '  macOS/Linux: lsof -ti:5100');
    process.exit(1);
  }

  log('success', '端口检查通过');

  const processes = [];

  // 准备环境变量
  const envVars = {
    VITE_ENV_FILE: envFile
  };

  // 启动Vue3开发服务器
  const vueProcess = startProcess('npm', ['run', 'dev:vue'], process.cwd(), 'Vue3服务器', envVars);
  processes.push(vueProcess);

  // 等待一下再启动React服务器
  setTimeout(() => {
    // 启动React开发服务器
    const reactProcess = startProcess('npm', ['run', 'dev:react'], process.cwd(), 'React服务器', envVars);
    processes.push(reactProcess);

    // 延迟进行健康检查
    setTimeout(() => {
      healthCheck();
    }, 10000);

  }, 3000);

  // 处理进程退出
  const cleanup = () => {
    log('info', '正在关闭开发服务器...');
    processes.forEach(proc => {
      if (proc && !proc.killed) {
        proc.kill('SIGTERM');
      }
    });

    setTimeout(() => {
      processes.forEach(proc => {
        if (proc && !proc.killed) {
          proc.kill('SIGKILL');
        }
      });
      process.exit(0);
    }, 5000);
  };

  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);
  process.on('uncaughtException', (err) => {
    log('error', '未捕获的异常:', err.message);
    cleanup();
  });

  log('info', '开发服务器信息:');
  log('info', `  当前环境: ${envFile}`);
  log('info', '  Vue3应用: http://localhost:5173');
  log('info', '  React应用: http://localhost:5173/gpt/');
  log('info', '  React直接访问: http://localhost:5100');
  log('info', '');
  log('info', '按 Ctrl+C 停止服务器');
}

main().catch((err) => {
  log('error', '启动失败:', err.message);
  process.exit(1);
});
