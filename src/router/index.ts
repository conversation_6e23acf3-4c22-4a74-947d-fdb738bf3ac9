import type { SinitekRouteRecordRaw } from 'sinitek-util'
import { loader } from 'sinitek-util'

const menuRoutes = loader.importRouteModules(import.meta.glob('src/router/routes/**/*.ts', { eager: true })) as Array<SinitekRouteRecordRaw>
const whiteRoutes = loader.importRouteModules(import.meta.glob('src/router/routes.ts', { eager: true })) as Array<SinitekRouteRecordRaw>

export { menuRoutes, whiteRoutes }
