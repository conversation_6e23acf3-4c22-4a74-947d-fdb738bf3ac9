import { zhCN, zhHK, enUS } from '@/i18n'
import icons from '@/icons'
import { dashboardModules } from '@/dashboard'
import { menuRoutes, whiteRoutes } from '@/router'
import { App } from 'vue'

const INSTALLED_KEY = Symbol('INSTALLED_KEY')

interface XnApp extends App {
    [INSTALLED_KEY]?: boolean
}

const template = {
  install: (Vue: XnApp) => {
    if (Vue[INSTALLED_KEY]) return
    Vue[INSTALLED_KEY] = true
  },
  getI18nZhCN: function () {
    return zhCN
  },
  getI18nZhHK: function () {
    return zhHK
  },
  getI18nEnUS: function () {
    return enUS
  },
  getIcons: function () {
    return icons
  },
  getWhiteRoutes: function () {
    return whiteRoutes
  },
  getMenuRoutes: function () {
    return menuRoutes
  },
  getDashboardModules: function () {
    return dashboardModules
  }
}

export default template
