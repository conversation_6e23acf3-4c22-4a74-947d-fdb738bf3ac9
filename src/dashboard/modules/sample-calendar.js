import { markRaw, defineAsyncComponent } from 'vue'

export default [
  {
    component: markRaw(defineAsyncComponent(() => import('src/views/samples/dashboard/DashboardCalendar.vue'))),
    key: 'dashboard/DashboardCalendar',
    more: 'example/calendar',
    title: '样品日历举例使用说明',
    buttons: [
      {
        key: 'more',
        customAction: true
      },
      {
        key: 'remove',
        icon: 'guanbi',
        title: '我是自定义的删除'
      },
      {
        key: 'calendarOpenWindow',
        icon: 'el-icon-edit',
        title: '自定义的编辑'
      }
    ]
  }
]
