import { App } from 'vue'
import { createStore } from 'vuex'
import { createI18n } from 'vue-i18n'
import { createRouter } from 'vue-router'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import SinitekUI from 'sinitek-ui'
import <PERSON>itekUtil, { securityUtil, token } from 'sinitek-util'
import Sirmapp from 'sirmapp'
import SinitekWorkflow from 'sinitek-workflow'
import SinitekMessage from 'sinitek-message'

import template from './index'
import instructions from './search-instructions'

export function handleGlobalUse (app: App) {
  securityUtil.startRouteParamCheck()
  const customComponents = {
    // login: CustomLogin,
    // headerDropdown: CustomHeaderDropdown,
    // tabBar: CustomTabBar,
    // AuthorityExplain: CustomAuthorityExplain
  }

  const routerOptions = { createRouter, options: {} }
  const vuexOptions = { createStore, options: {} }
  const i18nOptions = { createI18n, options: {} }

  const SinitekUIOptions = {
    filePreview: {
      isEnable: import.meta.env.VITE_IS_ENABLE_FILE_PREVIEW === 'true'
    },
    uploadChunkSize: 5
  }

  app.use(ElementPlus, {
    locale: zhCn
  })

  Sirmapp.importExamplePage()
  Sirmapp.setCustomCompatibilityMenuUrl(((url: string) => {
    const separator = url.includes('?') ? '&' : '?';
    return url + separator + 'accesstoken=' + token.getToken();
  }))

  const { router, store, i18n } = Sirmapp.init(app, {
    customComponents,
    vueOptions: {
      routerOptions,
      vuexOptions,
      i18nOptions
    },
    sinitekLibs: {
      SinitekWorkflow,
      SinitekMessage,
      SinitekUtil,
      SinitekUI,
      Sirmapp,
      template
    },
    libsOptions: {
      SinitekUIOptions
    }
  })
  // 系统公共配置中企业微信配置
  const mycommonConfigs = [{
    name: 'wxWorkConfig',
    component: Sirmapp.WxWorkConfig
  }]
  Sirmapp.appendCommonConfigs(mycommonConfigs)
  // 设置主题
  Sirmapp.setDynamicTheme(true)
  // 设置搜索栏指令配置
  Sirmapp.setSearchInstructions(instructions)

  return {
    router,
    store,
    i18n
  }
}
