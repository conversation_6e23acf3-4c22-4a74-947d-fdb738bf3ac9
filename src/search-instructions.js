export default [
  {
    resName: '菜单',
    resCode: 'menu',
    icon: 'submenu',
    sort: 1,
    instructions: [
      {
        code: 'open',
        name: '打开'
      }
    ]
  },
  {
    resName: '日程',
    resCode: 'schedule',
    icon: 'mybusiness',
    sort: 3,
    instructions: [
      {
        code: 'view',
        name: '查看',
        path: '/calendar/event/manager',
        executor (item) {
          console.log('执行自定义函数', item)
          this.$router.push(item.path).catch((err) => { console.warn(err) })
        }
      }
    ]
  },
  {
    resName: '用户',
    resCode: 'user',
    icon: 'people',
    sort: 2,
    instructions: [
      {
        code: 'view',
        name: '查看',
        sort: 2,
        url: '/frontend/api/user/list',
        filterKey: 'username',
        showKey: 'empname',
        path: '/user/list'
      },
      {
        code: 'create',
        name: '新建',
        sort: 1,
        path: '/user/list'
      },
      {
        code: 'edit',
        name: '编辑',
        sort: 3,
        url: '/frontend/api/user/list',
        filterKey: 'username',
        showKey: 'empname',
        path: '/user/list'
      }
    ]
  }
]
