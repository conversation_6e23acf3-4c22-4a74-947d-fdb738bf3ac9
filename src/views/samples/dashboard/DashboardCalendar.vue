<template>
  <div>
    <xn-calendar
      :mark-list="markList"
      @select="handlerSelect"
    />
    <div :style="{ width: '300px', border: '1px solid #d9d9d9', borderRadius: '4px' }">
      <el-card class="box-card">
        <template #header>
          <div class="clearfix">
            <span>日期: {{ selectDate }}</span>
          </div>
        </template>
      </el-card>
    </div>

    <xn-dialog
      v-model:show="param.show"
      :title="param.title"
      width="400px"
      :buttons="buttons"
      @cancel="closeDialog"
    >
      <template #dialog-form>
        我是一个通过首页模块自定义按钮点击后回显的窗口
      </template>
    </xn-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: 'DashboardCalendar',
  props: {
    testParam: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      buttons: [
        { label: '取消', type: 'info', action: 'cancel', event: 'cancel' }
      ],
      param: {
        title: '标题',
        show: false
      },
      markList: [
        '2021-07-01',
        '2021-07-28',
        '2021-07-25',
        '2021-07-19',
        '2022-08-19'
      ],
      selectDate: dayjs().format('YYYY-MM-DD')
    }
  },
  mounted () {
    console.log('我是testParam', this.testParam)
  },
  methods: {
    closeDialog () {
      this.param.show = false
    },
    handlerSelect (date) {
      this.selectDate = date.format('YYYY-MM-DD')
    },
    handleModuleBtnClick (event, module, btn) {
      console.log('我执行了handleModuleBtnClick, 我是btn', btn.key)
      if (btn.key === 'calendarOpenWindow') {
        this.param.show = true
      } else if (btn.key === 'calendarOpenWindowTwo') {
        console.log('我是calendarOpenWindowTwo按钮点击后要做的事情')
      }
    }
  }
}
</script>
