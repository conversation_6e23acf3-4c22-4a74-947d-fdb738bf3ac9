import { loader } from 'sinitek-util'

const zhCN = loader.importI18nModules(import.meta.glob('src/i18n/zh-cn/*.ts', { eager: true })) as Record<string, object>

const zhHK = loader.importI18nModules(import.meta.glob('src/i18n/zh-hk/*.ts', { eager: true })) as Record<string, object>

const enUS = loader.importI18nModules(import.meta.glob('src/i18n/en-us/*.ts', { eager: true })) as Record<string, object>

export { zhCN, zhHK, enUS }
